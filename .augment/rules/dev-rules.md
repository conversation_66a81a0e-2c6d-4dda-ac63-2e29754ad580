---
type: "always_apply"
---

# X1-Base Development Rules

## Package Naming Conventions
- Use lowercase letters only
- Be descriptive and specific to functionality (avoid generic names like `utils`, `common`, `base`)
- Use underscores sparingly and only when necessary for clarity
- Examples: `string_utils`, `validation`, `auth`, `redis_client`
- **Legacy Exception**: `base_utils` exists for historical reasons but should NOT be replicated in new code

## File Naming Conventions
- Use lowercase with underscores for separation
- Follow patterns: `{component}.go`, `{component}_test.go`, `interface.go`, `conf.go`
- Examples: `blb.go`, `blb_test.go`, `interface.go`, `logger_file.go`

## Import Organization
Order imports with blank line separation:
1. Standard library imports
2. Third-party imports (github.com, etc.)
3. Baidu internal imports (icode.baidu.com/baidu/gdp/*)
4. Project internal imports (icode.baidu.com/baidu/scs/x1-base/*)

## Component Architecture Pattern
All components must follow this singleton pattern:
```go
type component struct {
    conf     *config
    sdk      SomeSDK
    initOnce sync.Once
}

var defaultComponent = &component{
    conf: &config{},
}

func Instance() ComponentInterface {
    defaultComponent.initOnce.Do(func() {
        // Initialize component
    })
    return defaultComponent
}
```

## Error Handling Standards
- Use custom error system from `common/cerrs`
- Define component-specific errors: `cerrs.NewConst(code, message, isFatal, cause)`
- Wrap errors with context: `cerrs.ErrInvalidParams.Errorf("invalid parameter: %s", param)`
- Check error types: `cerrs.Is(err, cerrs.ErrNotFound)`

## Logging Conventions
Use appropriate loggers for different purposes:
- `logger.ComponentLogger` - Component operations
- `logger.SdkLogger` - SDK operations
- `logger.DefaultLogger` - General application logs
- Include context in all log calls: `logger.ComponentLogger.Warning(ctx, "message: %s", err)`

## Configuration Management
- Use TOML format for all configuration files
- Place configs in `conf_ut/component/` or `conf_ut/servicer/`
- Support IDC-specific overrides using `IDC_SPEC` sections
- Load with: `compo_utils.LoadConf("component-name", &config)`

## Testing Requirements
- Test files must end with `_test.go`
- Use table-driven tests for multiple scenarios
- Initialize tests with: `unittest.UnitTestInit(depth)`
- Use gomock for interface mocking
- Minimum 70% line coverage for new code, 90% for critical business logic

## Documentation Standards
File headers must include:
```go
/*
 * Copyright(C) YYYY Baidu Inc. All Rights Reserved.
 * Author: Name (<EMAIL>)
 * Date: YYYY/MM/DD
 * File: filename.go
 */
```

Public functions must have godoc comments with parameters and return values documented.

## Dependency Management
- Always use Go modules with `go mod download`
- Set Baidu proxy: `GOPROXY=https://goproxy.baidu-int.com`
- Pin specific versions for stability
- Approved external packages: testify, gomock, gin, gorm, uuid, cast

## Security Best Practices
- Use `icode.baidu.com/baidu/bce-iam/sdk-go` for authentication
- Validate all input parameters
- Use context with timeout for external calls
- Implement proper resource cleanup

## Performance Guidelines
- Use connection pooling for database and Redis
- Configure appropriate timeouts (ConnTimeOut=5000ms, ReadTimeOut=30000ms)
- Implement proper caching strategies
- Use sync.Once for component initialization

## Technical Debt - DO NOT REPLICATE
- `base_utils` package name violates naming conventions (exists for historical reasons)
- Some legacy files may not follow import organization (update when modifying)
- Old error handling patterns (use `cerrs` system for all new code)

## Code Review Checklist
- [ ] Follows naming conventions
- [ ] Uses `cerrs` error system
- [ ] Includes appropriate logging
- [ ] Has test coverage
- [ ] Documentation is complete
- [ ] No hardcoded values
- [ ] Proper resource cleanup