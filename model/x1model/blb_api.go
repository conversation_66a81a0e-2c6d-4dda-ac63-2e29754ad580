/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (<EMAIL>)
 * Date: 2021/12/06
 * File: blb_api.go
 */

/*
 * DESCRIPTION
 *   TODO file function desc
 */

// Package x1model TODO package function desc
package x1model

import "context"

func BLBGetByBLBId(ctx context.Context, blbId string) (*BLB, error) {
	var blb BLB
	err := resource.GetOneByUkey(ctx, blbId, &blb)
	if err != nil {
		return nil, err
	}
	return &blb, nil
}

func BLBGetByCond(ctx context.Context, fmtCond string, vals ...interface{}) (*BLB, error) {
	var blb BLB
	err := resource.GetOneByCond(ctx, &blb, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return &blb, nil
}

func BLBGetAllByCond(ctx context.Context, fmtCond string, vals ...interface{}) ([]*BLB, error) {
	var blbList []*BLB
	err := resource.GetAllByCond(ctx, &blbList, fmtCond, vals...)
	if err != nil {
		return nil, err
	}
	return blbList, nil
}

func BLBsSave(ctx context.Context, blbList []*BLB) error {
	return resource.FullSaveAssociationsSave(ctx, blbList)
}
