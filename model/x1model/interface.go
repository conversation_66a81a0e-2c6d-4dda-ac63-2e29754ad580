/*
 * Copyright(C) 2021 Baidu Inc. All Rights Reserved.
 * Author: <PERSON><PERSON> (ca<PERSON><PERSON><PERSON>@baidu.com)
 * Date: 2021/12/01
 * File: interface.go
 */

/*
 * DESCRIPTION
 *   数据结构
 */

// Package x1model
package x1model

import (
	"time"
)

type Application struct {
	Id                      int          `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	AppId                   string       `gorm:"column:app_id;NOT NULL" json:"app_id" ukey:"app_id"`
	AppName                 string       `gorm:"column:app_name;NOT NULL" json:"app_name"`
	Product                 string       `gorm:"column:product;default:XDB;NOT NULL" json:"product"`
	Type                    string       `gorm:"column:type;default:mysql;NOT NULL" json:"type"`
	AppMode                 string       `gorm:"column:app_mode;default:normal;NOT NULL" json:"app_mode"`
	Pool                    string       `gorm:"column:pool;NOT NULL" json:"pool"`
	Port                    int          `gorm:"column:port;NOT NULL" json:"port"`
	McpackPort              int          `gorm:"column:mcpack_port;NOT NULL" json:"mcpack_port"`
	InnerPort               int          `gorm:"column:inner_port;NOT NULL" json:"inner_port"`
	Azone                   string       `gorm:"column:azone;default:default" json:"azone"`
	Rzone                   string       `gorm:"column:rzone" json:"rzone"`
	VpcId                   string       `gorm:"column:vpc_id;default:default" json:"vpc_id"`
	UserId                  string       `gorm:"column:user_id" json:"user_id"`
	ZkHost                  string       `gorm:"column:zk_host;NOT NULL" json:"zk_host"`
	Status                  string       `gorm:"column:status;NOT NULL" json:"status"`
	Replicas                string       `gorm:"column:replicas;NOT NULL" json:"replicas"`
	DestReplicas            string       `gorm:"column:dest_replicas;NOT NULL" json:"dest_replicas"`
	Properties              string       `gorm:"column:properties" json:"properties"`
	CreateTime              time.Time    `gorm:"column:create_time;default:0000-00-00 00:00:00;NOT NULL" json:"create_time"`
	UpdateTime              time.Time    `gorm:"column:update_time;default:0000-00-00 00:00:00;NOT NULL" json:"update_time"`
	DeleteTime              time.Time    `gorm:"column:delete_time;default:0000-00-00 00:00:00;NOT NULL" json:"delete_time"`
	SemiStatus              string       `gorm:"column:semi_status;default:OFF" json:"semi_status"`
	IpType                  string       `gorm:"column:ip_type" json:"ip_type"`
	SecurityGroupId         string       `gorm:"column:security_group_id" json:"securityGroupId"`
	InternalSecurityGroupId string       `gorm:"column:internal_security_group_id" json:"internalSecurityGroupId"`
	AppShortID              int          `gorm:"column:app_short_id" json:"clusterShortId"`
	UserShortID             int          `gorm:"column:user_short_id" json:"userShortId"`
	ImageID                 string       `gorm:"column:image_id" json:"imageID"`
	DeploySetIds            string       `gorm:"column:deploy_set_ids" json:"deploySetIds"`
	Domain                  string       `gorm:"column:domain" json:"domain"`
	CloneDataAccess         string       `gorm:"column:clone_data_access" json:"clone_data_access"`
	AppGroupID              string       `gorm:"column:app_group_id" json:"app_group_id"`
	AppGroupSeqID           int          `gorm:"column:app_group_seq_id" json:"app_group_seq_id"`
	LocalMetaserver         string       `gorm:"column:local_metaserver" json:"local_metaserver"`
	GlobalMetaserver        string       `gorm:"column:global_metaserver" json:"global_metaserver"`
	Region                  string       `gorm:"column:region" json:"region"`
	BnsService              string       `gorm:"column:bns_service" json:"bns_service"`
	BLBs                    []*BLB       `gorm:"foreignKey:AppId;references:AppId"`
	Clusters                []*Cluster   `gorm:"foreignKey:AppId;references:AppId"`
	Interfaces              []*Interface `gorm:"foreignKey:AppId;references:AppId"`
	Entitys                 []*Entity    `gorm:"foreignKey:AppId;references:AppId"`
	ResourceType            string       `gorm:"column:resource_type" json:"resource_type"`
	SyncGroupID             string       `gorm:"column:sync_group_id" json:"sync_group_id"`
	LogFlag                 string       `gorm:"column:log_flag" json:"log_flag"`
	Entrance                string       `gorm:"column:entrance" json:"entrance"`
	AutoModifyStrategy      string       `gorm:"column:auto_modify_strategy" json:"auto_modify_strategy"`
	UseNewPackage           int          `gorm:"column:use_new_package" json:"use_new_package"`
	BcmInstanceGroup        string       `gorm:"column:bcm_instance_group;NOT NULL" json:"bcm_instance_group"`
	UseNewAgent             string       `gorm:"column:use_new_agent" json:"use_new_agent"`
	HtGrpMonAvailable       int          `gorm:"column:htgrp_mon_available" json:"htgrp_mon_available"`
	BcmCycle                int          `gorm:"column:bcm_cycle" json:"bcm_cycle"`
	ApiFlag                 string       `gorm:"column:api_flag" json:"api_flag"`
	DataserverType          string       `gorm:"column:dataserver_type" json:"dataserver_type"`
	SyncAgentBigver         string       `gorm:"column:sync_agent_bigver" json:"sync_agent_bigver"`
	DataReliabilityType     int          `gorm:"column:data_reliability_type" json:"data_reliability_type"`
}

func (Application) TableName() string {
	return "application"
}

type Cluster struct {
	Id                 int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	ClusterId          string    `gorm:"column:cluster_id;NOT NULL" json:"cluster_id" ukey:"cluster_id"`
	AppId              string    `gorm:"column:app_id" json:"app_id"`
	Engine             string    `gorm:"column:engine;NOT NULL" json:"engine"`
	EngineVersion      string    `gorm:"column:engine_version;NOT NULL" json:"engine_version"`
	EngineMinorVersion string    `gorm:"column:engine_minor_version;NOT NULL" json:"engine_minor_version"`
	Port               int       `gorm:"column:port;NOT NULL" json:"port"`
	XagentSyncPort     int       `gorm:"column:xagent_sync_port;default:0" json:"xagent_sync_port"`
	NodeRelation       string    `gorm:"column:node_relation;NOT NULL" json:"node_relation"`
	MemSize            int       `gorm:"column:mem_size;NOT NULL" json:"mem_size"`
	ActualMemSize      int       `gorm:"column:actual_mem_size;NOT NULL" json:"actual_mem_size"`
	DiskSize           int64     `gorm:"column:disk_size;NOT NULL" json:"disk_size"`
	ActualDiskSize     int       `gorm:"column:actual_disk_size;NOT NULL" json:"actual_disk_size"`
	DiskUsed           int64     `gorm:"column:disk_used;NOT NULL" json:"disk_used"`
	Cpu                int       `gorm:"column:cpu;default:1" json:"cpu"`
	ActualCpu          int       `gorm:"column:actual_cpu;default:1" json:"actual_cpu"`
	Status             string    `gorm:"column:status;NOT NULL" json:"status"`
	DestStatus         string    `gorm:"column:dest_status;NOT NULL" json:"dest_status"`
	Remark             string    `gorm:"column:remark;NOT NULL" json:"remark"`
	Properties         string    `gorm:"column:properties" json:"properties"`
	StoreType          string    `gorm:"column:store_type" json:"storeType"`
	AvailableVolume    int       `gorm:"column:available_volume" json:"availableVolume"`
	SysDiskSize        int       `gorm:"column:sys_disk_size" json:"sysDiskSize"`
	SysDiskUsed        int       `gorm:"column:sys_disk_used" json:"sysDiskUsed"`
	Spec               string    `gorm:"column:spec" json:"spec"`
	DestSpec           string    `gorm:"column:dest_spec" json:"destSpec"`
	ExpireTime         time.Time `gorm:"column:expire_time;default:0000-00-00 00:00:00" json:"expire_time"`
	CreateTime         time.Time `gorm:"column:create_time;default:0000-00-00 00:00:00;NOT NULL" json:"create_time"`
	UpdateTime         time.Time `gorm:"column:update_time;default:0000-00-00 00:00:00;NOT NULL" json:"update_time"`
	DeleteTime         time.Time `gorm:"column:delete_time;default:0000-00-00 00:00:00;NOT NULL" json:"delete_time"`
	BnsPath            string    `gorm:"column:bns_path;NOT NULL" json:"bns_path"`
	Bns                string    `gorm:"column:bns;NOT NULL" json:"bns"`
	Nodes              []*Node   `gorm:"foreignKey:ClusterId;references:ClusterId"`
	ClusterShortID     int       `gorm:"column:cluster_short_id" json:"clusterShortId"`
	MaxNodeIndex       int       `gorm:"column:max_node_index" json:"max_node_index"`
	GlobalID           string    `gorm:"column:global_id" json:"global_id"`
	GlobalSeqID        int       `gorm:"column:global_seq_id" json:"global_seq_id"`
	RoNodes            []*RoNode `gorm:"foreignKey:ClusterId;references:ClusterId"`
	BcmInstanceGroup   string    `gorm:"column:bcm_instance_group;NOT NULL" json:"bcm_instance_group"`
}

func (Cluster) TableName() string {
	return "cluster"
}

type Node struct {
	Id               int    `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	AppId            string `gorm:"column:app_id;NOT NULL" json:"app_id"`
	ClusterId        string `gorm:"column:cluster_id;NOT NULL" json:"cluster_id"`
	NodeId           string `gorm:"column:node_id;NOT NULL" json:"node_id" ukey:"node_id"`
	ContainerId      string `gorm:"column:container_id;NOT NULL" json:"container_id"`
	Engine           string `gorm:"column:engine;NOT NULL" json:"engine"`
	EngineVersion    string `gorm:"column:engine_version;NOT NULL" json:"engine_version"`
	Port             int    `gorm:"column:port;NOT NULL" json:"port"` // mysql
	Basedir          string `gorm:"column:basedir;NOT NULL" json:"basedir"`
	Datadir          string `gorm:"column:datadir;NOT NULL" json:"datadir"`
	InnodbBufferSize int    `gorm:"column:innodb_buffer_size;NOT NULL" json:"innodb_buffer_size"` // mysql innodb buffer pool size
	Ip               string `gorm:"column:ip;NOT NULL" json:"ip"`
	XagentPort       int    `gorm:"column:xagent_port;NOT NULL" json:"xagent_port"`
	Region           string `gorm:"column:region;default: ;NOT NULL" json:"region"`
	LogicZone        string `gorm:"column:logic_zone" json:"logic_zone"`
	Azone            string `gorm:"column:azone" json:"azone"`
	VpcId            string `gorm:"column:vpc_id" json:"vpc_id"`
	SubnetId         string `gorm:"column:subnet_id" json:"subnet_id"`
	Pool             string `gorm:"column:pool;default: ;NOT NULL" json:"pool"`
	Tags             string `gorm:"column:tags;NOT NULL" json:"tags"`
	Role             string `gorm:"column:role;NOT NULL" json:"role"`
	DestRole         string `gorm:"column:dest_role;NOT NULL" json:"dest_role"`
	Status           string `gorm:"column:status;NOT NULL" json:"status"`
	DestStatus       string `gorm:"column:dest_status;NOT NULL" json:"dest_status"`
	TaskId           int    `gorm:"column:task_id;NOT NULL" json:"task_id"`
	Properties       string `gorm:"column:properties" json:"properties"`
	ResourceOrderId  string `gorm:"column:resource_order_id" json:"resourceOrderId"`
	ResourceId       string `gorm:"column:resource_id" json:"resourceId"`
	FloatingIP       string `gorm:"column:floating_ip" json:"floatingIP"`
	IPv6             string `gorm:"column:ipv6" json:"ipv6"`
	RootPassword     string `gorm:"column:root_password" json:"rootPassword"`
	NodeShortID      int    `gorm:"column:node_short_id" json:"nodeShortId"`
	HostName         string `gorm:"column:host_name" json:"hostName"`
	NodeFixID        string `gorm:"column:node_fix_id" json:"nodeFixId"`
	TempFlags        string `gorm:"column:temp_flags" json:"tempFlags"`
	GlobalID         string `gorm:"column:global_id" json:"global_id"`
	GlobalSeqID      int    `gorm:"column:global_seq_id" json:"global_seq_id"`
	ContainerName    string `gorm:"column:container_name" json:"container_name"`
}

func (Node) TableName() string {
	return "node"
}

// RoNode definition
type RoNode struct {
	Id               int    `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	AppId            string `gorm:"column:app_id;NOT NULL" json:"app_id"`
	ClusterId        string `gorm:"column:cluster_id;NOT NULL" json:"cluster_id"`
	NodeId           string `gorm:"column:node_id;NOT NULL" json:"node_id" ukey:"node_id"`
	ContainerId      string `gorm:"column:container_id;NOT NULL" json:"container_id"`
	Engine           string `gorm:"column:engine;NOT NULL" json:"engine"`
	EngineVersion    string `gorm:"column:engine_version;NOT NULL" json:"engine_version"`
	Port             int    `gorm:"column:port;NOT NULL" json:"port"` // mysql
	Basedir          string `gorm:"column:basedir;NOT NULL" json:"basedir"`
	Datadir          string `gorm:"column:datadir;NOT NULL" json:"datadir"`
	InnodbBufferSize int    `gorm:"column:innodb_buffer_size;NOT NULL" json:"innodb_buffer_size"` // mysql innodb buffer pool size
	Ip               string `gorm:"column:ip;NOT NULL" json:"ip"`
	XagentPort       int    `gorm:"column:xagent_port;NOT NULL" json:"xagent_port"`
	Region           string `gorm:"column:region;default: ;NOT NULL" json:"region"`
	LogicZone        string `gorm:"column:logic_zone" json:"logic_zone"`
	Azone            string `gorm:"column:azone" json:"azone"`
	VpcId            string `gorm:"column:vpc_id" json:"vpc_id"`
	SubnetId         string `gorm:"column:subnet_id" json:"subnet_id"`
	Pool             string `gorm:"column:pool;default: ;NOT NULL" json:"pool"`
	Tags             string `gorm:"column:tags;NOT NULL" json:"tags"`
	Role             string `gorm:"column:role;NOT NULL" json:"role"`
	DestRole         string `gorm:"column:dest_role;NOT NULL" json:"dest_role"`
	Status           string `gorm:"column:status;NOT NULL" json:"status"`
	DestStatus       string `gorm:"column:dest_status;NOT NULL" json:"dest_status"`
	TaskId           int    `gorm:"column:task_id;NOT NULL" json:"task_id"`
	Properties       string `gorm:"column:properties" json:"properties"`
	ResourceOrderId  string `gorm:"column:resource_order_id" json:"resourceOrderId"`
	ResourceId       string `gorm:"column:resource_id" json:"resourceId"`
	FloatingIP       string `gorm:"column:floating_ip" json:"floatingIP"`
	IPv6             string `gorm:"column:ipv6" json:"ipv6"`
	RootPassword     string `gorm:"column:root_password" json:"rootPassword"`
	NodeShortID      int    `gorm:"column:node_short_id" json:"nodeShortId"`
	HostName         string `gorm:"column:host_name" json:"hostName"`
	NodeFixID        string `gorm:"column:node_fix_id" json:"nodeFixId"`
	TempFlags        string `gorm:"column:temp_flags" json:"tempFlags"`
	GlobalID         string `gorm:"column:global_id" json:"global_id"`
	GlobalSeqID      int    `gorm:"column:global_seq_id" json:"global_seq_id"`
	RoGroupID        string `gorm:"column:ro_group_id" json:"ro_group_id"`
	RoGroupWeight    int    `gorm:"column:ro_group_weight" json:"ro_group_weight"`
	RoGroupStatus    int    `gorm:"column:ro_group_status" json:"ro_group_status"`
	ContainerName    string `gorm:"column:container_name" json:"container_name"`
	BcmInstanceGroup string `gorm:"column:bcm_instance_group;NOT NULL" json:"bcm_instance_group"`
}

// TableName of RoNode
func (RoNode) TableName() string {
	return "ro_node"
}

type Interface struct {
	Id               int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	InterfaceId      string    `gorm:"column:interface_id;NOT NULL" json:"interface_id" ukey:"interface_id"`
	AccessType       string    `gorm:"column:access_type;NOT NULL" json:"access_type"` // proxy or blb or dns
	Engine           string    `gorm:"column:engine;NOT NULL" json:"engine"`           // dbproxy or bdrpproxy or bgw/f5/hapoxy or dns
	EngineVersion    string    `gorm:"column:engine_version;NOT NULL" json:"engine_version"`
	Port             int       `gorm:"column:port;NOT NULL" json:"port"`
	McpackPort       int       `gorm:"column:mcpack_port;NOT NULL" json:"mcpack_port"`
	AppId            string    `gorm:"column:app_id" json:"app_id"`
	ProxyRelation    string    `gorm:"column:proxy_relation;NOT NULL" json:"proxy_relation"`
	MemSize          int       `gorm:"column:mem_size;NOT NULL" json:"mem_size"`
	ActualMemSize    int       `gorm:"column:actual_mem_size;NOT NULL" json:"actual_mem_size"`
	DiskSize         int64     `gorm:"column:disk_size;NOT NULL" json:"disk_size"`
	ActualDiskSize   int       `gorm:"column:actual_disk_size;NOT NULL" json:"actual_disk_size"`
	DiskUsed         int       `gorm:"column:disk_used;NOT NULL" json:"disk_used"`
	Cpu              int       `gorm:"column:cpu;default:1" json:"cpu"`
	ActualCpu        int       `gorm:"column:actual_cpu;default:1" json:"actual_cpu"`
	Status           string    `gorm:"column:status;NOT NULL" json:"status"`
	Remark           string    `gorm:"column:remark;NOT NULL" json:"remark"`
	Properties       string    `gorm:"column:properties" json:"properties"`
	StoreType        string    `gorm:"column:store_type" json:"storeType"`
	AvailableVolume  int       `gorm:"column:available_volume" json:"availableVolume"`
	SysDiskSize      int       `gorm:"column:sys_disk_size" json:"sysDiskSize"`
	SysDiskUsed      int       `gorm:"column:sys_disk_used" json:"sysDiskUsed"`
	Spec             string    `gorm:"column:spec" json:"spec"`
	DestSpec         string    `gorm:"column:dest_spec" json:"destSpec"`
	CreateTime       time.Time `gorm:"column:create_time;default:0000-00-00 00:00:00;NOT NULL" json:"create_time"`
	UpdateTime       time.Time `gorm:"column:update_time;default:0000-00-00 00:00:00;NOT NULL" json:"update_time"`
	DeleteTime       time.Time `gorm:"column:delete_time;default:0000-00-00 00:00:00;NOT NULL" json:"delete_time"`
	BnsPath          string    `gorm:"column:bns_path;NOT NULL" json:"bns_path"`
	Bns              string    `gorm:"column:bns;NOT NULL" json:"bns"`
	Access           string    `gorm:"column:access" json:"access"`          // 存vip、domain
	ResourceId       string    `gorm:"column:resource_id" json:"resourceId"` // blb id
	InterfaceShortID int       `gorm:"column:interface_short_id" json:"interfaceShortId"`
	MaxNodeIndex     int       `gorm:"column:max_node_index" json:"max_node_index"`
	Proxys           []*Proxy  `gorm:"foreignKey:InterfaceId;references:InterfaceId"`
	BcmInstanceGroup string    `gorm:"column:bcm_instance_group;NOT NULL" json:"bcm_instance_group"`
}

func (Interface) TableName() string {
	return "interface"
}

type Proxy struct {
	Id               int    `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	ProxyId          string `gorm:"column:proxy_id;NOT NULL" json:"proxy_id" ukey:"proxy_id"`
	Ip               string `gorm:"column:ip;NOT NULL" json:"ip"`
	Port             int    `gorm:"column:port;NOT NULL" json:"port"`
	McpackPort       int    `gorm:"column:mcpack_port;NOT NULL" json:"mcpack_port"`
	StatPort         int    `gorm:"column:stat_port" json:"stat_port"`
	Region           string `gorm:"column:region;NOT NULL" json:"region"`
	LogicZone        string `gorm:"column:logic_zone" json:"logic_zone"`
	Azone            string `gorm:"column:azone" json:"azone"`
	VpcId            string `gorm:"column:vpc_id" json:"vpc_id"`
	XagentPort       int    `gorm:"column:xagent_port;NOT NULL" json:"xagent_port"`
	AppId            string `gorm:"column:app_id;NOT NULL" json:"app_id"`
	Properties       string `gorm:"column:properties" json:"properties"`
	Basedir          string `gorm:"column:basedir;NOT NULL" json:"basedir"`
	Status           string `gorm:"column:status;NOT NULL" json:"status"`
	DestStatus       string `gorm:"column:dest_status;NOT NULL" json:"dest_status"`
	ContainerId      string `gorm:"column:container_id;NOT NULL" json:"container_id"`
	InterfaceId      string `gorm:"column:interface_id;NOT NULL" json:"interface_id"` // from interface table
	ResourceOrderId  string `gorm:"column:resource_order_id" json:"resourceOrderId"`
	SubnetId         string `gorm:"column:subnet_id" json:"subnet_id"`
	ResourceId       string `gorm:"column:resource_id" json:"resourceId"`
	FloatingIP       string `gorm:"column:floating_ip" json:"floatingIP"`
	IPv6             string `gorm:"column:ipv6" json:"ipv6"`
	RootPassword     string `gorm:"column:root_password" json:"rootPassword"`
	Engine           string `gorm:"column:engine;NOT NULL" json:"engine"`
	EngineVersion    string `gorm:"column:engine_version;NOT NULL" json:"engine_version"`
	ProxyShortID     int    `gorm:"column:proxy_short_id;NOT NULL" json:"proxyShortId"`
	HostName         string `gorm:"column:host_name" json:"hostName"`
	NodeFixID        string `gorm:"column:node_fix_id" json:"nodeFixId"`
	TempFlags        string `gorm:"column:temp_flags" json:"tempFlags"`
	GlobalID         string `gorm:"column:global_id" json:"global_id"`
	GlobalSeqID      int    `gorm:"column:global_seq_id" json:"global_seq_id"`
	ContainerName    string `gorm:"column:container_name" json:"container_name"`
	BcmInstanceGroup string `gorm:"column:bcm_instance_group;NOT NULL" json:"bcm_instance_group"`
}

func (Proxy) TableName() string {
	return "proxy"
}

type Entity struct {
	Id         int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id" ukey:"id"`
	Name       string    `gorm:"column:name;NOT NULL" json:"name"`
	AppId      string    `gorm:"column:app_id;NOT NULL" json:"app_id"`
	ParentName string    `gorm:"column:parent_name" json:"parent_name"`
	Type       string    `gorm:"column:type;NOT NULL" json:"type"`
	Status     string    `gorm:"column:status;NOT NULL" json:"status"`
	Remark     string    `gorm:"column:remark;NOT NULL" json:"remark"`
	Etag       int       `gorm:"column:etag;default:0;NOT NULL" json:"etag"`
	CreateTime time.Time `gorm:"column:create_time;NOT NULL" json:"create_time"`
	UpdateTime time.Time `gorm:"column:update_time;NOT NULL" json:"update_time"`
	Properties string    `gorm:"column:properties;NOT NULL" json:"properties"`
	Charset    string    `gorm:"column:charset;NOT NULL" json:"charset"`
}

func (Entity) TableName() string {
	return "entity"
}

// Auth Redis鉴权信息表
type RedisAcl struct {
	Id             int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id" ukey:"id"`
	AppID          string    `gorm:"column:app_id;NOT NULL" json:"app_id"`
	AccountName    string    `gorm:"column:account_name;NOT NULL" json:"account_name"`
	CreateAt       time.Time `gorm:"column:create_at;NOT NULL" json:"create_at"`
	UpdateAt       time.Time `gorm:"column:update_at;NOT NULL" json:"update_at"`
	Version        int       `gorm:"column:version;default:0;NOT NULL" json:"version"`
	Engine         string    `gorm:"column:engine;NOT NULL" json:"engine"`
	Password       string    `gorm:"column:password;NOT NULL" json:"password"`
	AllowedCmds    string    `gorm:"column:allowed_cmds;NOT NULL" json:"allowed_cmds"`
	AllowedSubCmds string    `gorm:"column:allowed_sub_cmds;NOT NULL" json:"allowed_sub_cmds"`
	KeyPatterns    string    `gorm:"column:key_patterns;NOT NULL" json:"key_patterns"`
	Properties     string    `gorm:"column:properties;NOT NULL" json:"properties"`
	Status         string    `gorm:"column:status;NOT NULL" json:"status"`
}

func (RedisAcl) TableName() string {
	return "redis_acl"
}

type BLB struct {
	Id                     int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	AppId                  string    `gorm:"column:app_id;NOT NULL" json:"app_id"`
	Name                   string    `gorm:"column:name;NOT NULL" json:"name"`
	Type                   string    `gorm:"column:type;NOT NULL" json:"type"`
	VpcId                  string    `gorm:"column:vpc_id;NOT NULL" json:"vpc_id"`
	SubnetId               string    `gorm:"column:subnet_id;NOT NULL" json:"subnet_id"`
	IpType                 string    `gorm:"column:ip_type;NOT NULL" json:"ip_type"`
	BgwGroupId             string    `gorm:"column:bgw_group_id;NOT NULL" json:"bgw_group_id"`
	BgwGroupExclusive      int       `gorm:"column:bgw_group_exclusive;default:0" json:"bgw_group_exclusive"`
	BgwGroupMode           string    `gorm:"column:bgw_group_mode;NOT NULL" json:"bgw_group_mode"`
	MasterAZ               string    `gorm:"column:master_az;NOT NULL" json:"master_az"`
	SlaveAZ                string    `gorm:"column:slave_az;NOT NULL" json:"slave_az"`
	BlbId                  string    `gorm:"column:blb_id;NOT NULL" json:"blb_id"`
	Vip                    string    `gorm:"column:vip;NOT NULL" json:"vip"`
	Ovip                   string    `gorm:"column:ovip;NOT NULL" json:"ovip"` // pnetip
	Ipv6                   string    `gorm:"column:ipv6;NOT NULL" json:"ipv6"`
	Status                 string    `gorm:"column:status;NOT NULL" json:"status"`
	CreateAt               time.Time `gorm:"column:create_at;NOT NULL" json:"create_at"`
	UpdateAt               time.Time `gorm:"column:update_at;NOT NULL" json:"update_at"`
	RoGroupID              string    `gorm:"column:ro_group_id;NOT NULL;default:''" json:"ro_group_id"`
	IPGroupID              string    `gorm:"column:ip_group_id;NOT NULL;default:''" json:"ip_group_id"`
	ResourceUserId         string    `gorm:"column:resource_user_id" json:"resource_user_id"`
	ResourceVpcId          string    `gorm:"column:resource_vpc_id" json:"resource_vpc_id"`
	ResourceSubnetId       string    `gorm:"column:resource_subnet_id" json:"resource_subnet_id"`
	ServicePublishEndpoint string    `gorm:"column:service_publish_endpoint" json:"service_publish_endpoint"`
	EndpointId             string    `gorm:"column:endpoint_id" json:"endpoint_id"`
	EndpointIp             string    `gorm:"column:endpoint_ip" json:"endpoint_ip"`
	McpackIPGroupID        string    `gorm:"column:mcpack_ip_group_id;NOT NULL;default:''" json:"mcpack_ip_group_id"`
}

func (BLB) TableName() string {
	return "blb"
}

type Config struct {
	Id    int    `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	AppId string `gorm:"column:app_id;NOT NULL" json:"app_id"`
	Name  string `gorm:"column:name;NOT NULL" json:"name"`
	Type  string `gorm:"column:type;NOT NULL" json:"type"`
	Value string `gorm:"column:value;NOT NULL" json:"value"`
}

func (Config) TableName() string {
	return "config"
}

type Backup struct {
	Id       int           `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	AppId    string        `gorm:"column:app_id;NOT NULL" json:"app_id"`
	BackupID string        `gorm:"column:backup_id;NOT NULL" json:"backup_id"`
	CreateAt time.Time     `gorm:"column:create_at;NOT NULL" json:"create_at"`
	UpdateAt time.Time     `gorm:"column:update_at;NOT NULL" json:"update_at"`
	EndAt    time.Time     `gorm:"column:end_at;NOT NULL" json:"end_at"`
	ExpireAt time.Time     `gorm:"column:expire_at;NOT NULL" json:"expire_at"`
	Status   string        `gorm:"column:status;NOT NULL" json:"status"`
	Type     string        `gorm:"column:type;NOT NULL" json:"type"`
	Comment  string        `gorm:"column:comment;NOT NULL" json:"comment"`
	Items    []*BackupItem `gorm:"foreignKey:BackupID;references:BackupID"`
}

func (Backup) TableName() string {
	return "backup"
}

type BackupItem struct {
	Id        int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	AppId     string    `gorm:"column:app_id;NOT NULL" json:"app_id"`
	BackupID  string    `gorm:"column:backup_id;NOT NULL" json:"backup_id"`
	ClusterId string    `gorm:"column:cluster_id;NOT NULL" json:"cluster_id"`
	NodeID    string    `gorm:"column:node_id;NOT NULL" json:"node_id"`
	CreateAt  time.Time `gorm:"column:create_at;NOT NULL" json:"create_at"`
	UpdateAt  time.Time `gorm:"column:update_at;NOT NULL" json:"update_at"`
	Access    string    `gorm:"column:access;NOT NULL" json:"access"`
	Size      int64     `gorm:"column:size;NOT NULL" json:"size"`
	Status    string    `gorm:"column:status;NOT NULL" json:"status"`
}

func (BackupItem) TableName() string {
	return "backup_item"
}

type Exp struct {
	Id          int         `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	StrategyId  string      `gorm:"column:strategy_id;NOT NULL" json:"strategy_id" ukey:"strategy_id"`
	DefaultFlag string      `gorm:"column:default_flag;NOT NULL" json:"default_flag"`
	ExpOwner    string      `gorm:"column:exp_owner;NOT NULL" json:"exp_owner"`
	CreateTime  time.Time   `gorm:"column:create_time;default:0000-00-00 00:00:00;NOT NULL" json:"create_time"`
	UpdateTime  time.Time   `gorm:"column:update_time;default:0000-00-00 00:00:00;NOT NULL" json:"update_time"`
	DeleteTime  time.Time   `gorm:"column:delete_time;default:0000-00-00 00:00:00;NOT NULL" json:"delete_time"`
	Rules       []*ExpRules `gorm:"foreignKey:StrategyId;references:StrategyId"`
}

func (Exp) TableName() string {
	return "exp"
}

type ExpRules struct {
	Id         int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	StrategyId string    `gorm:"column:strategy_id;NOT NULL" json:"strategy_id"`
	Ranking    int       `gorm:"column:ranking;default:0;NOT NULL" json:"ranking"`
	Rules      string    `gorm:"column:rules;NOT NULL" json:"rules"`
	Flag       string    `gorm:"column:flag;NOT NULL" json:"flag"`
	CreateTime time.Time `gorm:"column:create_time;default:0000-00-00 00:00:00;NOT NULL" json:"create_time"`
	UpdateTime time.Time `gorm:"column:update_time;default:0000-00-00 00:00:00;NOT NULL" json:"update_time"`
	DeleteTime time.Time `gorm:"column:delete_time;default:0000-00-00 00:00:00;NOT NULL" json:"delete_time"`
}

func (ExpRules) TableName() string {
	return "exp_rules"
}

type MetaCluster struct {
	Id            int         `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	MetaClusterID string      `gorm:"column:meta_cluster_id;NOT NULL" json:"meta_cluster_id" ukey:"meta_cluster_id"`
	Name          string      `gorm:"column:name;NOT NULL" json:"name"`
	Desc          string      `gorm:"column:desc;NOT NULL" json:"desc"`
	Status        string      `gorm:"column:status;NOT NULL" json:"status"`
	Type          string      `gorm:"column:type;NOT NULL" json:"type"`
	UserId        string      `gorm:"column:user_id" json:"user_id"`
	Engine        string      `gorm:"column:engine;NOT NULL" json:"engine"`
	Region        string      `gorm:"column:region;default: ;NOT NULL" json:"region"`
	EngineVersion string      `gorm:"column:engine_version;NOT NULL" json:"engine_version"`
	Entrance      string      `gorm:"column:entrance;NOT NULL" json:"entrance"`
	CurMaster     string      `gorm:"column:cur_master;NOT NULL" json:"cur_master"`
	Quorum        int         `gorm:"column:quorum;NOT NULL" json:"quorum"`
	CreatedAt     time.Time   `gorm:"column:created_at;NOT NULL" json:"create_time"`
	UpdatedAt     time.Time   `gorm:"column:updated_at;NOT NULL" json:"update_time"`
	Password      string      `gorm:"column:password; NOT NULL" json:"password"`
	MetaNodes     []*MetaNode `gorm:"foreignKey:MetaClusterID;references:MetaClusterID"`
}

func (MetaCluster) TableName() string {
	return "meta_cluster"
}

type MetaNode struct {
	Id              int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	MetaNodeID      string    `gorm:"column:meta_node_id;NOT NULL" json:"meta_node_id" ukey:"meta_node_id"`
	MetaClusterID   string    `gorm:"column:meta_cluster_id;NOT NULL" json:"meta_cluster_id"`
	Status          string    `gorm:"column:status;NOT NULL" json:"status"`
	Role            string    `gorm:"column:role;NOT NULL" json:"role"`
	Engine          string    `gorm:"column:engine;NOT NULL" json:"engine"`
	EngineVersion   string    `gorm:"column:engine_version;NOT NULL" json:"engine_version"`
	Port            int       `gorm:"column:port;NOT NULL" json:"port"`
	Region          string    `gorm:"column:region;default: ;NOT NULL" json:"region"`
	LogicZone       string    `gorm:"column:logic_zone" json:"logic_zone"`
	Azone           string    `gorm:"column:azone" json:"azone"`
	VpcId           string    `gorm:"column:vpc_id" json:"vpc_id"`
	SubnetId        string    `gorm:"column:subnet_id" json:"subnet_id"`
	Basedir         string    `gorm:"column:basedir;NOT NULL" json:"basedir"`
	Datadir         string    `gorm:"column:datadir;NOT NULL" json:"datadir"`
	Ip              string    `gorm:"column:ip;NOT NULL" json:"ip"`
	FloatingIP      string    `gorm:"column:floating_ip" json:"floatingIP"`
	IPv6            string    `gorm:"column:ipv6" json:"ipv6"`
	ResourceOrderId string    `gorm:"column:resource_order_id" json:"resourceOrderId"`
	ResourceId      string    `gorm:"column:resource_id" json:"resourceId"`
	CreateTime      time.Time `gorm:"column:create_time;NOT NULL" json:"create_time"`
	UpdateTime      time.Time `gorm:"column:update_time;NOT NULL" json:"update_time"`
}

func (MetaNode) TableName() string {
	return "meta_node"
}

type Package struct {
	Id           int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	Name         string    `gorm:"column:name;NOT NULL" json:"name"`
	PackageID    string    `gorm:"column:package_id;NOT NULL" json:"package_id"`
	FullVersion  string    `gorm:"column:full_version;NOT NULL" json:"full_version"`
	MajorVersion string    `gorm:"column:major_version;NOT NULL" json:"major_version"`
	ReleasedAt   time.Time `gorm:"column:released_at;NOT NULL" json:"released_at"`
	Access       string    `gorm:"column:access;NOT NULL" json:"access"`
	Status       string    `gorm:"column:status;NOT NULL" json:"status"`
	Tags         string    `gorm:"column:tags;NOT NULL" json:"tags"`
	Description  string    `gorm:"column:description;NOT NULL" json:"description"`
	Owner        string    `gorm:"column:owner;NOT NULL" json:"owner"`
	MD5          string    `gorm:"column:md5;NOT NULL" json:"md5"`
	CoreBinMD5   string    `gorm:"column:core_bin_md5;NOT NULL" json:"core_bin_md5"`
	TplID        string    `gorm:"column:tpl_id;NOT NULL" json:"tpl_id"`
}

func (Package) TableName() string {
	return "package"
}

type PackageRecord struct {
	ID         int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	Entity     string    `gorm:"column:entity;NOT NULL" json:"entity"`
	Name       string    `gorm:"column:name;NOT NULL" json:"name"`
	Type       string    `gorm:"column:type;NOT NULL" json:"type"`
	PackageID  string    `gorm:"column:package_id;NOT NULL" json:"version_id"`
	Status     string    `gorm:"column:status;NOT NULL" json:"status"`
	CreatedAt  time.Time `gorm:"column:created_at;NOT NULL" json:"created_at"`
	Owner      string    `gorm:"column:owner;NOT NULL" json:"owner"`
	Deploytime int64     `gorm:"column:deploytime;default:0;NOT NULL"` // 部署时间，bigint形的unix时间戳
}

func (PackageRecord) TableName() string {
	return "package_record"
}

type Injection struct {
	Id            int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	UserID        string    `gorm:"column:user_id;NOT NULL" json:"user_id"`
	AppID         string    `gorm:"column:app_id;NOT NULL" json:"app_id"`
	Status        string    `gorm:"column:status;NOT NULL" json:"status"`
	LastInjection time.Time `gorm:"column:last_injection;NOT NULL" json:"last_injection"`
}

func (Injection) TableName() string {
	return "injection"
}

type ConfTpl struct {
	Id        int            `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	TplID     string         `gorm:"column:tpl_id;NOT NULL" json:"tpl_id"`
	CreatedAt time.Time      `gorm:"column:created_at;NOT NULL" json:"created_at"`
	UpdatedAt time.Time      `gorm:"column:updated_at;NOT NULL" json:"updated_at"`
	Status    string         `gorm:"column:status;NOT NULL" json:"status"`
	Desc      string         `gorm:"column:desc;NOT NULL" json:"desc"`
	PkgType   string         `gorm:"column:pkg_type;NOT NULL" json:"pkg_type"`
	Items     []*ConfTplItem `gorm:"foreignKey:TplID;references:TplID"`
}

func (ConfTpl) TableName() string {
	return "conf_tpl"
}

type ConfTplItem struct {
	Id         int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	TplID      string    `gorm:"column:tpl_id;NOT NULL" json:"tpl_id"`
	DeployPath string    `gorm:"column:deploy_path;NOT NULL" json:"deploy_path"`
	RenderType string    `gorm:"column:render_type;NOT NULL" json:"render_type"`
	CreatedAt  time.Time `gorm:"column:created_at;NOT NULL" json:"created_at"`
	UpdatedAt  time.Time `gorm:"column:updated_at;NOT NULL" json:"updated_at"`
	Content    string    `gorm:"column:content;NOT NULL" json:"content"`
}

func (ConfTplItem) TableName() string {
	return "conf_tpl_item"
}

type ProxyAcl struct {
	Id             int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id" ukey:"id"`
	AppID          string    `gorm:"column:app_id;NOT NULL" json:"app_id"`
	AccountName    string    `gorm:"column:account_name;NOT NULL" json:"account_name"`
	CreateAt       time.Time `gorm:"column:create_at;NOT NULL" json:"create_at"`
	UpdateAt       time.Time `gorm:"column:update_at;NOT NULL" json:"update_at"`
	Version        int       `gorm:"column:version;default:0;NOT NULL" json:"version"`
	Engine         string    `gorm:"column:engine;NOT NULL" json:"engine"`
	Password       string    `gorm:"column:password;NOT NULL" json:"password"`
	AllowedCmds    string    `gorm:"column:allowed_cmds;NOT NULL" json:"allowed_cmds"`
	AllowedSubCmds string    `gorm:"column:allowed_sub_cmds;NOT NULL" json:"allowed_sub_cmds"`
	KeyPatterns    string    `gorm:"column:key_patterns;NOT NULL" json:"key_patterns"`
	Properties     string    `gorm:"column:properties;NOT NULL" json:"properties"`
	Status         string    `gorm:"column:status;NOT NULL" json:"status"`
}

func (ProxyAcl) TableName() string {
	return "proxy_acl"
}

type DeploySet struct {
	ID          int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id" ukey:"id"`
	Name        string    `gorm:"column:name;NOT NULL" json:"name"`
	DeploySetID string    `gorm:"column:deployset_id;NOT NULL" json:"deployset_id"`
	UserID      string    `gorm:"column:user_id;NOT NULL" json:"user_id"`
	Status      string    `gorm:"column:status;NOT NULL" json:"status"`
	Strategy    string    `gorm:"column:strategy;NOT NULL" json:"strategy"`
	Concurrency int       `gorm:"column:concurrency;NOT NULL" json:"concurrency"`
	Desc        string    `gorm:"column:description;NOT NULL" json:"description"`
	CreateAt    time.Time `gorm:"column:create_at;NOT NULL" json:"create_at"`
	UpdateAt    time.Time `gorm:"column:update_at;NOT NULL" json:"update_at"`
}

func (DeploySet) TableName() string {
	return "deployset"
}
