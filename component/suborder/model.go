package suborder

import (
	"context"
	"time"

	"icode.baidu.com/baidu/scs/x1-base/model"
	"icode.baidu.com/baidu/scs/x1-base/model/x1model"
)

const (
	OrderStatusToCreate    = "to_create"
	OrderStatusCreating    = "creating"
	OrderStatusCreated     = "created"
	OrderStatusToRollback  = "to_rollback"
	OrderStatusRollbacking = "rollbacking"
	OrderStatusRollbacked  = "rollbacked"
	OrderStatusError       = "error"
)

var FailedStatus = []string{OrderStatusToRollback, OrderStatusError, OrderStatusRollbacking, OrderStatusRollbacked}

type OrderRecord struct {
	Id         int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	OrderID    string    `gorm:"column:order_id;NOT NULL" json:"order_id"`
	CreatedAt  time.Time `gorm:"column:created_at;NOT NULL" json:"create_time"`
	Status     string    `gorm:"column:status;NOT NULL" json:"status"`
	TaskID     string    `gorm:"column:task_id;NOT NULL" json:"task_id"`
	Azone      string    `gorm:"column:azone;NOT NULL" json:"azone"`
	AppID      string    `gorm:"column:app_id;NOT NULL" json:"app_id"`
	UserID     string    `gorm:"column:user_id;NOT NULL" json:"user_id"`
	Parameters string    `gorm:"column:parameters;NOT NULL" json:"parameters"`
	ErrMsg     string    `gorm:"column:err_msg;NOT NULL" json:"err_msg"`
}

func (OrderRecord) TableName() string {
	return "order_record"
}

type SubOrderRecord struct {
	Id          int       `gorm:"column:id;AUTO_INCREMENT;primary_key" json:"id"`
	OrderID     string    `gorm:"column:order_id;NOT NULL" json:"order_id"`
	SplitKey    string    `gorm:"column:split_key;NOT NULL" json:"split_key"`
	Retry       int       `gorm:"column:retry;NOT NULL" json:"retry"`
	POrderID    string    `gorm:"column:p_order_id;NOT NULL" json:"p_order_id"`
	BccOrderID  string    `gorm:"column:bcc_order_id;NOT NULL" json:"bcc_order_id"`
	CreatedAt   time.Time `gorm:"column:created_at;NOT NULL" json:"create_time"`
	Parameters  string    `gorm:"column:parameters;NOT NULL" json:"parameters"`
	Status      string    `gorm:"column:status;NOT NULL" json:"status"`
	BccRequest  string    `gorm:"column:bcc_request;NOT NULL" json:"bcc_request"`
	BccResponse string    `gorm:"column:bcc_response;NOT NULL" json:"bcc_response"`
	ErrMsg      string    `gorm:"column:err_msg;NOT NULL" json:"err_msg"`
}

func (SubOrderRecord) TableName() string {
	return "sub_order_record"
}

type ModelService interface {
	GetOrderByTaskID(ctx context.Context, orderID string) (*OrderRecord, error)
	GetOrderByID(ctx context.Context, orderID string) (*OrderRecord, error)
	SaveOrder(ctx context.Context, order *OrderRecord) (*OrderRecord, error)
	GetSubOrdersByOrderID(ctx context.Context, orderID string) ([]*SubOrderRecord, error)
	SaveSubOrders(ctx context.Context, orders []*SubOrderRecord) error
}

type modelService struct{}

func (s *modelService) GetOrderByID(ctx context.Context, orderID string) (*OrderRecord, error) {
	var records []*OrderRecord
	if err := bccOrderModel.GetAllByCond(ctx, &records, "order_id = ?", orderID); err != nil {
		return nil, err
	}
	if len(records) == 0 {
		return nil, nil
	}
	return records[0], nil
}

func (s *modelService) GetOrderByTaskID(ctx context.Context, orderID string) (*OrderRecord, error) {
	var records []*OrderRecord
	if err := bccOrderModel.GetAllByCond(ctx, &records, "task_id = ?", orderID); err != nil {
		return nil, err
	}
	if len(records) == 0 {
		return nil, nil
	}
	return records[0], nil
}

func (s *modelService) SaveOrder(ctx context.Context, order *OrderRecord) (*OrderRecord, error) {
	if err := bccOrderModel.FullSaveAssociationsSave(ctx, order); err != nil {
		return nil, err
	}
	return order, nil
}

func (s *modelService) GetSubOrdersByOrderID(ctx context.Context, orderID string) ([]*SubOrderRecord, error) {
	var records []*SubOrderRecord
	if err := bccOrderModel.GetAllByCond(ctx, &records, "p_order_id = ?", orderID); err != nil {
		return nil, err
	}
	return records, nil
}

func (s *modelService) SaveSubOrders(ctx context.Context, orders []*SubOrderRecord) error {
	if len(orders) == 0 {
		return nil
	}
	if err := bccOrderModel.FullSaveAssociationsSave(ctx, orders); err != nil {
		return err
	}
	return nil
}

var bccOrderModel *model.Resource

func MustInitSubOrderModel(ctx context.Context, conf *x1model.Conf) {
	resourcePtr, err := model.InitModel(ctx, model.ResourceCfg{
		ServicerName: func() string {
			if conf.ServicerName != "" {
				return conf.ServicerName
			}
			return "bcc-order-model"
		}(),
		DbLogger:    conf.DbLogger,
		GormLogger:  conf.GormLogger,
		PreloadConf: nil,
		AutoPreload: false,
	})
	if err != nil {
		panic("init X1model fail")
	}
	bccOrderModel = resourcePtr
}

func NewModelService() ModelService {
	return &modelService{}
}
